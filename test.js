import { SolanaRentCollector } from './ClaimSol.js';
import pkg from '@solana/web3.js';
const { Connection, PublicKey, Keypair } = pkg;

async function testRentCollector() {
  try {
    // 1. <PERSON>ết nối tới <PERSON> (sử dụng devnet để test)
    const connection = new Connection('https://api.devnet.solana.com', 'confirmed');
    
    // 2. Cấu hình phí rent
    const rentConfig = {
      rentPercentage: 0.1, // 0.1%
      minimumRent: 0.001, // 0.001 SOL minimum
      maximumRent: 0.1, // 0.1 SOL maximum
      collectorWallet: new PublicKey('********************************') // System Program ID làm ví dụ
    };

    const rentCollector = new SolanaRentCollector(connection, rentConfig);

    // 3. Test tính toán phí
    console.log('=== TEST TÍNH TOÁN PHÍ ===');
    const testTradeValue = 1.5; // 1.5 SOL
    const calculatedFee = rentCollector.calculateRentFee(testTradeValue);
    console.log(`Giao dịch: ${testTradeValue} SOL`);
    console.log(`Phí rent: ${calculatedFee} SOL`);
    
    // 4. Test với các giá trị khác nhau
    const testValues = [0.1, 1.0, 5.0, 100.0];
    console.log('\n=== TEST NHIỀU GIÁ TRỊ ===');
    testValues.forEach(value => {
      const fee = rentCollector.calculateRentFee(value);
      console.log(`${value} SOL -> ${fee} SOL phí`);
    });

    // 5. Test tạo instruction (không gửi transaction thật)
    console.log('\n=== TEST TẠO INSTRUCTION ===');
    const dummyTrader = new PublicKey('********************************');
    const instruction = await rentCollector.createRentCollectionInstruction(
      dummyTrader,
      0.001
    );
    console.log('Instruction tạo thành công:', instruction.programId.toString());

    console.log('\n✅ Tất cả test đều thành công!');
    
  } catch (error) {
    console.error('❌ Lỗi trong quá trình test:', error);
  }
}

// Chạy test
testRentCollector();
