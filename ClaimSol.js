import pkg from '@solana/web3.js';
const {
  Connection,
  PublicKey,
  Keypair,
  Transaction,
  SystemProgram,
  LAMPORTS_PER_SOL,
} = pkg;

import splToken from '@solana/spl-token';
const {
  getAssociatedTokenAddress,
  createAssociatedTokenAccountInstruction,
  createTransferInstruction,
  TOKEN_PROGRAM_ID,
} = splToken;

// RentConfig object structure:
// {
//   rentPercentage: number, // Phần trăm phí rent (ví dụ: 0.1 = 0.1%)
//   minimumRent: number,    // Phí rent tối thiểu (SOL)
//   maximumRent: number,    // Phí rent tối đa (SOL)
//   collectorWallet: PublicKey // Ví nhận phí
// }

// TradeInfo object structure:
// {
//   trader: PublicKey,
//   tokenMint: PublicKey,
//   tradeAmount: number,    // Số lượng token
//   solAmount: number,      // Gi<PERSON> trị SOL tương ứng
//   tradeType: 'BUY' | 'SELL'
// }

class SolanaRentCollector {
  constructor(connection, rentConfig) {
    this.connection = connection;
    this.rentConfig = rentConfig;
  }

  /**
   * Tính toán phí rent dựa trên giá trị giao dịch
   */
  calculateRentFee(tradeValue) {
    const calculatedFee = tradeValue * (this.rentConfig.rentPercentage / 100);

    // Áp dụng min/max limits
    if (calculatedFee < this.rentConfig.minimumRent) {
      return this.rentConfig.minimumRent;
    }

    if (calculatedFee > this.rentConfig.maximumRent) {
      return this.rentConfig.maximumRent;
    }

    return calculatedFee;
  }

  /**
   * Tạo instruction thu phí rent bằng SOL
   */
  async createRentCollectionInstruction(traderPublicKey, rentFeeInSOL) {
    const rentFeeInLamports = Math.floor(rentFeeInSOL * LAMPORTS_PER_SOL);

    return SystemProgram.transfer({
      fromPubkey: traderPublicKey,
      toPubkey: this.rentConfig.collectorWallet,
      lamports: rentFeeInLamports,
    });
  }

  /**
   * Tạo instruction thu phí rent bằng token
   */
  async createTokenRentCollectionInstruction(
    traderPublicKey,
    tokenMint,
    rentFeeInTokens,
    decimals
  ) {
    const traderTokenAccount = await getAssociatedTokenAddress(
      tokenMint,
      traderPublicKey
    );

    const collectorTokenAccount = await getAssociatedTokenAddress(
      tokenMint,
      this.rentConfig.collectorWallet
    );

    const instructions = [];

    // Kiểm tra xem collector có token account chưa
    try {
      await this.connection.getAccountInfo(collectorTokenAccount);
    } catch {
      // Tạo token account nếu chưa có
      instructions.push(
        createAssociatedTokenAccountInstruction(
          traderPublicKey, // payer
          collectorTokenAccount,
          this.rentConfig.collectorWallet,
          tokenMint
        )
      );
    }

    // Tạo instruction transfer token
    const transferAmount = Math.floor(rentFeeInTokens * Math.pow(10, decimals));
    instructions.push(
      createTransferInstruction(
        traderTokenAccount,
        collectorTokenAccount,
        traderPublicKey,
        transferAmount
      )
    );

    return instructions;
  }

  /**
   * Xử lý thu phí cho một giao dịch memecoin
   */
  async processTradeRentCollection(
    tradeInfo,
    payerKeypair,
    feePaymentMethod = 'SOL'
  ) {
    try {
      const rentFee = this.calculateRentFee(tradeInfo.solAmount);

      console.log(`Thu phí rent: ${rentFee} SOL cho giao dịch ${tradeInfo.tradeType}`);
      console.log(`Trader: ${tradeInfo.trader.toString()}`);
      console.log(`Token: ${tradeInfo.tokenMint.toString()}`);

      const transaction = new Transaction();

      if (feePaymentMethod === 'SOL') {
        const rentInstruction = await this.createRentCollectionInstruction(
          tradeInfo.trader,
          rentFee
        );
        transaction.add(rentInstruction);
      } else {
        // Thu phí bằng token (giả sử 1 SOL = 1000000 tokens)
        const tokenDecimals = 6; // Thường memecoin có 6 decimals
        const tokenRentFee = rentFee * 1000000; // Conversion rate

        const tokenInstructions = await this.createTokenRentCollectionInstruction(
          tradeInfo.trader,
          tradeInfo.tokenMint,
          tokenRentFee,
          tokenDecimals
        );
        transaction.add(...tokenInstructions);
      }

      // Lấy recent blockhash
      const { blockhash } = await this.connection.getLatestBlockhash();
      transaction.recentBlockhash = blockhash;
      transaction.feePayer = payerKeypair.publicKey;

      // Ký và gửi transaction
      transaction.sign(payerKeypair);
      const signature = await this.connection.sendRawTransaction(
        transaction.serialize()
      );

      // Confirm transaction
      await this.connection.confirmTransaction(signature, 'confirmed');

      console.log(`Thu phí thành công! Signature: ${signature}`);
      return signature;

    } catch (error) {
      console.error('Lỗi khi thu phí rent:', error);
      throw error;
    }
  }

  /**
   * Thu phí hàng loạt cho nhiều giao dịch
   */
  async processBatchRentCollection(trades, payerKeypair) {
    const signatures = [];

    for (const trade of trades) {
      try {
        const signature = await this.processTradeRentCollection(
          trade,
          payerKeypair
        );
        signatures.push(signature);
      } catch (error) {
        console.error(`Lỗi thu phí cho trader ${trade.trader.toString()}:`, error);
        continue;
      }
    }

    return signatures;
  }

  /**
   * Lấy thông tin balance của collector wallet
   */
  async getCollectorBalance() {
    const solBalance = await this.connection.getBalance(this.rentConfig.collectorWallet);

    // Lấy tất cả token accounts
    const tokenAccounts = await this.connection.getParsedTokenAccountsByOwner(
      this.rentConfig.collectorWallet,
      { programId: TOKEN_PROGRAM_ID }
    );

    const tokens = new Map();
    for (const tokenAccount of tokenAccounts.value) {
      const accountData = tokenAccount.account.data;
      const mint = accountData.parsed.info.mint;
      const amount = accountData.parsed.info.tokenAmount.uiAmount;
      tokens.set(mint, amount);
    }

    return {
      sol: solBalance / LAMPORTS_PER_SOL,
      tokens
    };
  }

  /**
   * Rút tiền từ collector wallet
   */
  async withdrawCollectedFees(
    collectorKeypair,
    destinationWallet,
    amount // Nếu không có thì rút hết
  ) {
    const balance = await this.connection.getBalance(collectorKeypair.publicKey);
    const withdrawAmount = amount ?
      Math.floor(amount * LAMPORTS_PER_SOL) :
      balance - 5000; // Giữ lại 5000 lamports cho phí

    const transaction = new Transaction().add(
      SystemProgram.transfer({
        fromPubkey: collectorKeypair.publicKey,
        toPubkey: destinationWallet,
        lamports: withdrawAmount,
      })
    );

    const { blockhash } = await this.connection.getLatestBlockhash();
    transaction.recentBlockhash = blockhash;
    transaction.feePayer = collectorKeypair.publicKey;

    transaction.sign(collectorKeypair);
    const signature = await this.connection.sendRawTransaction(
      transaction.serialize()
    );

    await this.connection.confirmTransaction(signature, 'confirmed');
    return signature;
  }
}

// Ví dụ sử dụng
async function example() {
  // Kết nối tới Solana
  const connection = new Connection('https://api.mainnet-beta.solana.com', 'confirmed');

  // Cấu hình phí rent
  const rentConfig = {
    rentPercentage: 0.1, // 0.1%
    minimumRent: 0.001, // 0.001 SOL minimum
    maximumRent: 0.1, // 0.1 SOL maximum
    collectorWallet: new PublicKey('Your_Collector_Wallet_Address_Here')
  };

  const rentCollector = new SolanaRentCollector(connection, rentConfig);

  // Thông tin giao dịch memecoin
  const tradeInfo = {
    trader: new PublicKey('Trader_Wallet_Address'),
    tokenMint: new PublicKey('Memecoin_Mint_Address'),
    tradeAmount: 1000000, // 1M tokens
    solAmount: 1.5, // Tương đương 1.5 SOL
    tradeType: 'BUY'
  };

  // Keypair để ký transaction (có thể là trader hoặc authority)
  const payerKeypair = Keypair.fromSecretKey(
    // Your secret key here
    new Uint8Array([])
  );

  try {
    // Thu phí rent
    const signature = await rentCollector.processTradeRentCollection(
      tradeInfo,
      payerKeypair,
      'SOL'
    );

    console.log('Phí rent đã được thu thành công:', signature);

    // Kiểm tra balance collector
    const balance = await rentCollector.getCollectorBalance();
    console.log('Collector balance:', balance);

  } catch (error) {
    console.error('Lỗi:', error);
  }
}

export { SolanaRentCollector };